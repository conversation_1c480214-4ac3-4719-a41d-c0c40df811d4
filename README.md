Run with docker compose up

Client

Server

Como criar um novo resource (por exemplo, `product`):

---

### 1. **Create Controller**

**`src/controllers/productController.ts`**

	import { Request, Response } from "express";
	import prisma from "../config/database.js";
	
	export const createProduct =  async (req: Request, res: Response) => { };
	export const getAllProducts =  async (req: Request, res: Response) => { };
	export const getProductById =  async (req: Request, res: Response) => { };
	export const updateProduct =  async (req: Request, res: Response) => { };
	export const deleteProduct =  async (req: Request, res: Response) => { };


### 2. **Create Routes**

**`src/routes/productRoutes.ts`**
	
	import { Router } from "express";
	import {
	  createProduct,
	  getAllProducts,
	  getProductById,
	  updateProduct,
	  deleteProduct
	} from "../controllers/productController.js";
	
	const router = Router();
	router.post("/", createProduct);
	router.get("/", getAllProducts);
	router.get("/:id", getProductById);
	router.put("/:id", updateProduct);
	router.delete("/:id", deleteProduct);
	
	export default router;

### 3. **Register the Routes in [app.ts](vscode-file://vscode-app/opt/visual-studio-code/resources/app/out/vs/code/electron-sandbox/workbench/workbench.html)**

	import productRoutes from "./routes/productRoutes.js";
	// ...existing code...
	app.use("/api/products", productRoutes);

---

### 4. **Add Model to `schema.prisma`**

	model Product {
	  id    Int    @id @default(autoincrement())
	  name  String
	  price Float
	  // ...other fields...
	}

### 5. **Por fim, rode os comandos:`**

	task create:prisma -- NOME_DO_RECURSO
 	task rebuild

---


