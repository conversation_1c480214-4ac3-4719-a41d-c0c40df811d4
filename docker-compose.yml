services:
    postgres:
        container_name: database
        ports:
            - "5431:5432"
        image: postgres
        environment:
            POSTGRES_USER: "${POSTGRES_USER}"
            POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"
            POSTGRES_DB: ${POSTGRES_DB}
        volumes:
            - ./docker_test_db:/var/lib/postgresql/data
        healthcheck:
            test: ["CMD-SHELL", "sh -c 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}'"]
            interval: 5s
            timeout: 60s
            retries: 5
            start_period: 80s
    server:
        container_name: server
        build:
            context: ./server
            dockerfile: Dockerfile
        ports:
            - "7999:8000"
        command: bash -c "npm run build && npm start"
        environment:
            NODE_ENV: "development"
            DATABASE_URL: "${DATABASE_URL}"
            PORT: "${SERVER_PORT}"
        depends_on:
            postgres:
                condition: service_healthy
    client:
        container_name: client
        build:
            context: ./client
            dockerfile: Dockerfile
        command: npm run dev
        ports:
            - "5173:5173"
        depends_on:
            - server
        volumes:
            - ./client:/client
            - client_node_modules:/client/node_modules

volumes:
  client_node_modules:
