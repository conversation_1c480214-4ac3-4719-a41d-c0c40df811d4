# Testing Auth Endpoints

## 1. Register a new user
```bash
curl -X POST http://localhost:4000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "gender": "<PERSON><PERSON>",
    "phone": "+1234567890"
  }'
```

Expected response (201):
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

## 2. Login with the user
```bash
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

Expected response (200):
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 3. Test protected route (example)
```bash
# First, get the token from login response, then:
curl -X GET http://localhost:4000/api/protected-route \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 4. Test error cases

### Invalid credentials:
```bash
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }'
```

Expected response (401):
```json
{
  "error": "Invalid credentials"
}
```

### Duplicate email registration:
```bash
# Try to register the same email again
curl -X POST http://localhost:4000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "Jane",
    "last_name": "Smith",
    "gender": "FEMALE",
    "phone": "+0987654321"
  }'
```

Expected response (400):
```json
{
  "error": "Email already in use"
}
```

## 5. Test middleware protection

To test the auth middleware, you'll need to create a protected route. Add this to your user routes:

```typescript
// In userRoutes.ts
import authMiddleware from '../middlewares/auth.middleware.js';

// Add this line before your existing routes:
router.use(authMiddleware);
```

Then test:
```bash
# Without token (should fail):
curl -X GET http://localhost:4000/api/users/all

# With token (should work):
curl -X GET http://localhost:4000/api/users/all \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
