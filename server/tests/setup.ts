import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';

// Test database instance
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
    },
  },
});

// Global test setup
beforeAll(async () => {
  // Connect to test database
  await testPrisma.$connect();
  console.log('Connected to test database');
});

afterAll(async () => {
  // Disconnect from test database
  await testPrisma.$disconnect();
  console.log('Disconnected from test database');
});

// Clean up database before each test
beforeEach(async () => {
  // Clean up test data in reverse order of dependencies
  await testPrisma.user.deleteMany({});
});

afterEach(async () => {
  // Additional cleanup if needed
});

// Helper function to create test user
export const createTestUser = async (overrides = {}) => {
  const defaultUser = {
    email: `test-${Date.now()}@example.com`,
    password: 'hashedPassword123',
    first_name: 'Test',
    last_name: 'User',
    gender: 'MALE' as const,
    phone: '+1234567890',
    ...overrides,
  };

  return await testPrisma.user.create({
    data: defaultUser,
  });
};

// Helper function to clean up specific test data
export const cleanupTestData = async () => {
  await testPrisma.user.deleteMany({
    where: {
      email: {
        contains: 'test-',
      },
    },
  });
};
