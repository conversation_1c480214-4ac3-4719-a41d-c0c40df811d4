import { describe, it, expect, beforeEach } from 'vitest';
import { hashPassword, verifyPassword } from '../src/utils/auth.utils.js';
import J<PERSON>THelper from '../src/utils/jwt-helper.js';

describe('Auth Integration', () => {
  describe('Password flow', () => {
    it('should hash and verify password correctly', async () => {
      const password = 'testPassword123';
      
      const hashedPassword = await hashPassword(password);
      const isValid = await verifyPassword(password, hashedPassword);
      const isInvalid = await verifyPassword('wrongPassword', hashedPassword);
      
      expect(isValid).toBe(true);
      expect(isInvalid).toBe(false);
    });
  });

  describe('JWT flow', () => {
    const secret = 'test-secret-key';
    
    it('should create and verify JWT token', () => {
      const userId = 123;
      const organizationId = 'test-org';
      const mockUser = { id: userId, email: '<EMAIL>' };
      
      const token = JWTHelper.createUserToken(mockUser as any, organizationId, {}, secret);
      const decoded = JWTHelper.decode(token, {}, secret);
      
      expect(decoded.sub).toBe(String(userId));
      expect(decoded.organization).toBe(organizationId);
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp).toBeDefined();
      expect(decoded.exp).toBeGreaterThan(Number(decoded.iat));
    });

    it('should handle token expiration', () => {
      const mockUser = { id: 123, email: '<EMAIL>' };
      const token = JWTHelper.encode(mockUser.id, { exp: Math.floor(Date.now() / 1000) - 3600 }, secret);
      expect(() => JWTHelper.decode(token, {}, secret)).toThrow();
    });

    it('should refresh token correctly', async () => {
      const mockUser = { id: 123, email: '<EMAIL>' };
      const originalToken = JWTHelper.createUserToken(mockUser as any, 'test-org', {}, secret);

      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait a bit to ensure different timestamps
      const refreshedToken = JWTHelper.refreshToken(originalToken, true, secret);

      const originalDecoded = JWTHelper.decode(originalToken, {}, secret);
      const refreshedDecoded = JWTHelper.decode(refreshedToken, {}, secret);

      expect(refreshedDecoded.sub).toBe(originalDecoded.sub);
      expect(refreshedDecoded.organization).toBe(originalDecoded.organization);
      expect(refreshedDecoded.iat).toBeGreaterThan(Number(originalDecoded.iat));
      expect(refreshedDecoded.exp).toBeGreaterThan(Number(originalDecoded.exp));
    });
  });

  describe('Complete auth flow simulation', () => {
    it('should simulate user registration and login flow', async () => {
      const userPassword = 'userPassword123';
      const userId = 456;
      const organizationId = 'user-org';
      const hashedPassword = await hashPassword(userPassword);
      
      const mockUser = {
        id: userId,
        email: '<EMAIL>',
        password: hashedPassword,
        organization: organizationId
      };
      
      const loginPasswordValid = await verifyPassword(userPassword, mockUser.password);
      expect(loginPasswordValid).toBe(true);
      
      const token = JWTHelper.createUserToken(mockUser as any, mockUser.organization);
      const decodedToken = JWTHelper.decode(token);
      
      expect(decodedToken.sub).toBe(String(userId));
      expect(decodedToken.organization).toBe(organizationId);
      
      const wrongPasswordCheck = await verifyPassword('wrongPassword', mockUser.password);
      expect(wrongPasswordCheck).toBe(false);
    });
  });
});
