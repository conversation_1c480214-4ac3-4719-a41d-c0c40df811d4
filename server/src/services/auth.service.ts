import { Gender, PrismaClient } from '@prisma/client';
import { hashPassword, verifyPassword } from '../utils/auth.utils.js';
import  J<PERSON>THelper  from '../utils/jwt-helper.js';

const prisma = new PrismaClient();

interface UserParams {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  gender: Gender;
  phone: string;
}

export async function registerUser({  
  email,
  password,
  first_name,
  last_name,
  gender,
  phone
} : UserParams) {
  const existingUser = await prisma.user.findUnique({ where: { email } });
  
 if (existingUser) {
    throw new Error('Email already in use');
 }

 const hashedPassword = await hashPassword(password);
 console.log("registerUser hashpassword", hashedPassword)

 return prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      first_name,
      last_name,
      gender,
      phone
    },
    select: {
      id: true,
      email: true,
      first_name: true,
      last_name:true,
      createdAt: true,
    },
 });
}

export async function loginUser(email: string, password: string) {
 const user = await prisma.user.findUnique({ where: { email } });
 
 if (!user) {
    throw new Error('Invalid credentials');
 }

 const isValid = await verifyPassword(password, user.password);
 
 if (!isValid) {
    throw new Error('Invalid credentials');
 }

 return JWTHelper.createUserToken(user, user.organization);
}
