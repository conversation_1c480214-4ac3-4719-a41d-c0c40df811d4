import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { registerUser, loginUser } from './auth.service.js';
import * as authUtils from '../utils/auth.utils.js';
import J<PERSON>THelper from '../utils/jwt-helper.js';

// Mock Prisma
const mockPrisma = {
  user: {
    findUnique: vi.fn(),
    create: vi.fn(),
  },
} as any;

// Mock the PrismaClient constructor
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn(() => mockPrisma),
  Gender: {
    MALE: 'MALE',
    FEMALE: 'FEMALE',
    OTHER: 'OTHER',
  },
}));

// Mock auth utils
vi.mock('../utils/auth.utils.js', () => ({
  hashPassword: vi.fn(),
  verifyPassword: vi.fn(),
}));

// Mock JWT Helper
vi.mock('../utils/jwt-helper.js', () => ({
  default: {
    createUserToken: vi.fn(),
  },
}));

describe('Auth Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('registerUser', () => {
    const mockUserData = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'John',
      last_name: 'Doe',
      gender: 'MALE' as const,
      phone: '+1234567890',
    };

    it('should successfully register a new user', async () => {
      // Arrange
      const hashedPassword = 'hashedPassword123';
      const createdUser = {
        id: 1,
        email: mockUserData.email,
        first_name: mockUserData.first_name,
        last_name: mockUserData.last_name,
        createdAt: new Date(),
      };

      mockPrisma.user.findUnique.mockResolvedValue(null); // No existing user
      vi.mocked(authUtils.hashPassword).mockResolvedValue(hashedPassword);
      mockPrisma.user.create.mockResolvedValue(createdUser);

      // Act
      const result = await registerUser(mockUserData);

      // Assert
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: mockUserData.email },
      });
      expect(authUtils.hashPassword).toHaveBeenCalledWith(mockUserData.password);
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: mockUserData.email,
          password: hashedPassword,
          first_name: mockUserData.first_name,
          last_name: mockUserData.last_name,
          gender: mockUserData.gender,
          phone: mockUserData.phone,
        },
        select: {
          id: true,
          email: true,
          first_name: true,
          last_name: true,
          createdAt: true,
        },
      });
      expect(result).toEqual(createdUser);
    });

    it('should throw error if email already exists', async () => {
      // Arrange
      const existingUser = { id: 1, email: mockUserData.email };
      mockPrisma.user.findUnique.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(registerUser(mockUserData)).rejects.toThrow('Email already in use');
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: mockUserData.email },
      });
      expect(authUtils.hashPassword).not.toHaveBeenCalled();
      expect(mockPrisma.user.create).not.toHaveBeenCalled();
    });

    it('should handle database errors during user creation', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);
      vi.mocked(authUtils.hashPassword).mockResolvedValue('hashedPassword');
      mockPrisma.user.create.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(registerUser(mockUserData)).rejects.toThrow('Database error');
    });
  });

  describe('loginUser', () => {
    const email = '<EMAIL>';
    const password = 'password123';
    const mockUser = {
      id: 1,
      email,
      password: 'hashedPassword123',
      organization: 'test-org',
      first_name: 'John',
      last_name: 'Doe',
    };

    it('should successfully login with valid credentials', async () => {
      // Arrange
      const mockToken = 'jwt-token-123';
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      vi.mocked(authUtils.verifyPassword).mockResolvedValue(true);
      vi.mocked(JWTHelper.createUserToken).mockReturnValue(mockToken);

      // Act
      const result = await loginUser(email, password);

      // Assert
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email },
      });
      expect(authUtils.verifyPassword).toHaveBeenCalledWith(password, mockUser.password);
      expect(JWTHelper.createUserToken).toHaveBeenCalledWith(mockUser, mockUser.organization);
      expect(result).toBe(mockToken);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(loginUser(email, password)).rejects.toThrow('Invalid credentials');
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email },
      });
      expect(authUtils.verifyPassword).not.toHaveBeenCalled();
      expect(JWTHelper.createUserToken).not.toHaveBeenCalled();
    });

    it('should throw error if password is invalid', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      vi.mocked(authUtils.verifyPassword).mockResolvedValue(false);

      // Act & Assert
      await expect(loginUser(email, password)).rejects.toThrow('Invalid credentials');
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email },
      });
      expect(authUtils.verifyPassword).toHaveBeenCalledWith(password, mockUser.password);
      expect(JWTHelper.createUserToken).not.toHaveBeenCalled();
    });

    it('should handle database errors during user lookup', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));

      // Act & Assert
      await expect(loginUser(email, password)).rejects.toThrow('Database connection failed');
    });
  });
});
