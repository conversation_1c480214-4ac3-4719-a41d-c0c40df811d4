import express from "express";
import cors from "cors";
import dotenv from "dotenv";

import userRoutes from "./routes/userRoutes.js";

dotenv.config();

const app = express();
const router = express.Router();

app.use(cors());
app.use(express.json());

app.use("/", router);
app.use("/api", userRoutes);

printRoutes(app._router.stack);

app.listen(process.env.PORT || 4000, () => {
    console.log(`Listening to port: ${process.env.PORT}`);
});


function printRoutes(stack:any, prefix = "") {
    stack.forEach((layer: any) => {
      if (layer.route && layer.route.path) {
        Object.keys(layer.route.methods).forEach(method => {
          console.log(`${method.toUpperCase()} ${prefix}${layer.route.path}`);
        });
      } else if (layer.name === "router" && layer.handle.stack) {
        printRoutes(layer.handle.stack, prefix + (layer.regexp.source === '^\\/' ? '' : layer.regexp.source.replace(/\\\//g, '/').replace('^', '').replace('?(?=\\/|$)', '')));
      }
    });
  }