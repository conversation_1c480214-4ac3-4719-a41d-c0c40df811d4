import { PrismaClient, User } from '@prisma/client';
import { Request, Response, NextFunction } from 'express';

import{verifyToken} from '../utils/auth.utils.js'; 


interface CustomRequest extends Request {
  user?: User;
}

const authMiddleware = async(req:CustomRequest, res:Response, next:NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const decodedToken = await verifyToken(token);

    req.user = decodedToken.user;
    
    next();
  } catch (err) {
    console.error('Auth middleware error:', err);
    res.status(401).json({ message: 'Token is not valid' });
  }
};

module.exports = authMiddleware;
