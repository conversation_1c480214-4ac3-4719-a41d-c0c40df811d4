import { PrismaClient } from '@prisma/client';
import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils/auth.utils.js';

const prisma = new PrismaClient();

interface AuthenticatedUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  organization: string | null;
  gender: string;
  phone: string;
  createdAt: Date;
}

interface CustomRequest extends Request {
  user?: AuthenticatedUser;
}

const authMiddleware = async (req: CustomRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const decodedToken = verifyToken(token);

    const user = await prisma.user.findUnique({
      where: { id: parseInt(decodedToken.sub as string) },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        organization: true,
        gender: true,
        phone: true,
        createdAt: true,
      }
    });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    req.user = user;
    next();
  } catch (err) {
    console.error('Auth middleware error:', err);
    res.status(401).json({ message: 'Token is not valid' });
  }
};

export default authMiddleware;
