import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Request, Response, NextFunction } from 'express';

// Mock Prisma
const mockPrisma = {
  user: {
    findUnique: vi.fn(),
  },
} as any;

// Mock the PrismaClient constructor
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => mockPrisma),
}));

// Mock auth utils
vi.mock('../utils/auth.utils.js', () => ({
  verifyToken: vi.fn(),
}));

// Import after mocking
const authMiddleware = await import('./auth.middleware.js');
const authUtils = await import('../utils/auth.utils.js');

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockRequest = {
      header: vi.fn(),
    };
    
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };
    
    mockNext = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should successfully authenticate user with valid token', async () => {
    // Arrange
    const token = 'valid.jwt.token';
    const decodedToken = { sub: '123' };
    const user = {
      id: 123,
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      organization: 'test-org',
      gender: 'MALE',
      phone: '+1234567890',
      createdAt: new Date(),
    };

    mockRequest.header = vi.fn().mockReturnValue(`Bearer ${token}`);
    vi.mocked(authUtils.verifyToken).mockReturnValue(decodedToken);
    mockPrisma.user.findUnique.mockResolvedValue(user);

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).toHaveBeenCalledWith(token);
    expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
      where: { id: 123 },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        organization: true,
        gender: true,
        phone: true,
        createdAt: true,
      },
    });
    expect(mockRequest.user).toEqual(user);
    expect(mockNext).toHaveBeenCalled();
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it('should return 401 when no token is provided', async () => {
    // Arrange
    mockRequest.header = vi.fn().mockReturnValue(undefined);

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).not.toHaveBeenCalled();
    expect(mockPrisma.user.findUnique).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: 'No token, authorization denied',
    });
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should return 401 when token is malformed (no Bearer prefix)', async () => {
    // Arrange
    mockRequest.header = vi.fn().mockReturnValue('invalid-token-format');

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).toHaveBeenCalledWith('invalid-token-format');
  });

  it('should return 401 when token verification fails', async () => {
    // Arrange
    const token = 'invalid.jwt.token';
    mockRequest.header = vi.fn().mockReturnValue(`Bearer ${token}`);
    vi.mocked(authUtils.verifyToken).mockImplementation(() => {
      throw new Error('Invalid token');
    });

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).toHaveBeenCalledWith(token);
    expect(mockPrisma.user.findUnique).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: 'Token is not valid',
    });
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should return 401 when user is not found in database', async () => {
    // Arrange
    const token = 'valid.jwt.token';
    const decodedToken = { sub: '999' };

    mockRequest.header = vi.fn().mockReturnValue(`Bearer ${token}`);
    vi.mocked(authUtils.verifyToken).mockReturnValue(decodedToken);
    mockPrisma.user.findUnique.mockResolvedValue(null);

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).toHaveBeenCalledWith(token);
    expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
      where: { id: 999 },
      select: {
        id: true,
        email: true,
        first_name: true,
        last_name: true,
        organization: true,
        gender: true,
        phone: true,
        createdAt: true,
      },
    });
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: 'User not found',
    });
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should handle database errors gracefully', async () => {
    // Arrange
    const token = 'valid.jwt.token';
    const decodedToken = { sub: '123' };

    mockRequest.header = vi.fn().mockReturnValue(`Bearer ${token}`);
    vi.mocked(authUtils.verifyToken).mockReturnValue(decodedToken);
    mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(mockRequest.header).toHaveBeenCalledWith('Authorization');
    expect(authUtils.verifyToken).toHaveBeenCalledWith(token);
    expect(mockPrisma.user.findUnique).toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: 'Token is not valid',
    });
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should handle non-numeric user ID in token', async () => {
    // Arrange
    const token = 'valid.jwt.token';
    const decodedToken = { sub: 'invalid-id' };

    mockRequest.header = vi.fn().mockReturnValue(`Bearer ${token}`);
    vi.mocked(authUtils.verifyToken).mockReturnValue(decodedToken);
    mockPrisma.user.findUnique.mockResolvedValue(null);

    // Act
    await authMiddleware.default(mockRequest as any, mockResponse as any, mockNext);

    // Assert
    expect(authUtils.verifyToken).toHaveBeenCalledWith(token);
    expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
      where: { id: NaN }, // parseInt('invalid-id') returns NaN
      select: expect.any(Object),
    });
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: 'User not found',
    });
  });
});
