const jwt = require('jsonwebtoken');

type Supporter = { id: string | number };
type Organization = { id: string | number };


class JWTHelper {
  static EXPIRATION = 60 * 60 * 24 * 30; // 30 days
  static ENCRYPTION_ALGORITHM = 'HS256';

  static getSecretKey(secretKey: string | null) {
    if (secretKey) {
      return secretKey;
    }
    return process.env.GRASSROOTS_JWT_SECRET_KEY || 'dev-only-jwt-secret-key';
  }

  static encode(identifier: string|number, extraClaims = {}, secretKey:string | null = null) {
    const epochNow = Math.floor(Date.now() / 1000);
    secretKey = this.getSecretKey(secretKey);

    const payload = {
      sub: identifier,
      iat: epochNow,
      exp: epochNow + this.EXPIRATION,
      ...extraClaims
    };

    return jwt.sign(payload, secretKey, { algorithm: this.ENCRYPTION_ALGORITHM });
  }

  static decode(token: string, extraOptions = {}, secretKey:string | null  = null) {
    secretKey = this.getSecretKey(secretKey);
    const options = {
      algorithms: [this.ENCRYPTION_ALGORITHM],
      ...extraOptions
    };

    try {
      return jwt.verify(token, secretKey, options);
    } catch (error: any) {
      if (error instanceof jwt.TokenExpiredError) {
        console.error('Expired JWT token', error);
      } else if (error instanceof jwt.JsonWebTokenError) {
        console.error('Invalid JWT token', error);
      } else {
        console.error(`Generic JWT error: ${error.message}`, error);
      }
      throw error;
    }
  }

  static createSupporterToken(supporter: Supporter, organization: Organization, extraData = {}, secretKey:string | null = null) {
    secretKey = this.getSecretKey(secretKey);

    const claims = {
      organization: organization.id,
      ...extraData
    };

    return this.encode(
      supporter.id,
      claims,
      secretKey
    );
  }

  static refreshToken(token: string, extendExpiration = true, secretKey = null) {
    try {
      const payload = this.decode(token, { ignoreExpiration: true }, secretKey);

      const epochNow = Math.floor(Date.now() / 1000);
      payload.iat = epochNow;

      if (extendExpiration) {
        payload.exp = epochNow + this.EXPIRATION;
      }

      return jwt.sign(
        payload,
        this.getSecretKey(secretKey),
        { algorithm: this.ENCRYPTION_ALGORITHM }
      );
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        console.error('Cannot refresh invalid token', error);
      }
      throw error;
    }
  }
}

module.exports = JWTHelper;
