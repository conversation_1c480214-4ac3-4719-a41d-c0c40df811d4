import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcrypt';
import { hashPassword, verifyPassword, verifyToken } from './auth.utils.js';
import JWTHelper from './jwt-helper.js';

vi.mock('bcrypt', () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
}));

vi.mock('./jwt-helper.js', () => ({
  default: {
    decode: vi.fn(),
  },
}));

describe('Auth Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash password with correct salt rounds', async () => {
      // Arrange
      const password = 'mySecretPassword';
      const hashedPassword = '$2b$12$hashedPasswordExample';
      vi.mocked(bcrypt.hash).mockResolvedValue(hashedPassword as never);

      // Act
      const result = await hashPassword(password);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 12);
      expect(result).toBe(hashedPassword);
    });

    it('should handle bcrypt errors', async () => {
      // Arrange
      const password = 'mySecretPassword';
      const error = new Error('Bcrypt error');
      vi.mocked(bcrypt.hash).mockRejectedValue(error);

      // Act & Assert
      await expect(hashPassword(password)).rejects.toThrow('Bcrypt error');
    });
  });

  describe('verifyPassword', () => {
    it('should return true for valid password', async () => {
      // Arrange
      const password = 'mySecretPassword';
      const hash = '$2b$12$hashedPasswordExample';
      vi.mocked(bcrypt.compare).mockResolvedValue(true as never);

      // Act
      const result = await verifyPassword(password, hash);

      // Assert
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(true);
    });

    it('should return false for invalid password', async () => {
      // Arrange
      const password = 'wrongPassword';
      const hash = '$2b$12$hashedPasswordExample';
      vi.mocked(bcrypt.compare).mockResolvedValue(false as never);

      // Act
      const result = await verifyPassword(password, hash);

      // Assert
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(false);
    });

    it('should handle bcrypt comparison errors', async () => {
      // Arrange
      const password = 'mySecretPassword';
      const hash = '$2b$12$hashedPasswordExample';
      const error = new Error('Bcrypt comparison error');
      vi.mocked(bcrypt.compare).mockRejectedValue(error);

      // Act & Assert
      await expect(verifyPassword(password, hash)).rejects.toThrow('Bcrypt comparison error');
    });
  });

  describe('verifyToken', () => {
    it('should successfully verify valid token', () => {
      // Arrange
      const token = 'valid.jwt.token';
      const secret = 'test-secret';
      const decodedPayload = {
        sub: '123',
        iat: 1234567890,
        exp: 1234567890 + 3600,
        organization: 'test-org',
      };
      vi.mocked(JWTHelper.decode).mockReturnValue(decodedPayload);

      // Act
      const result = verifyToken(token, secret);

      // Assert
      expect(JWTHelper.decode).toHaveBeenCalledWith(token, {}, secret);
      expect(result).toEqual(decodedPayload);
    });

    it('should verify token without secret parameter', () => {
      // Arrange
      const token = 'valid.jwt.token';
      const decodedPayload = {
        sub: '123',
        iat: 1234567890,
        exp: 1234567890 + 3600,
      };
      vi.mocked(JWTHelper.decode).mockReturnValue(decodedPayload);

      // Act
      const result = verifyToken(token);

      // Assert
      expect(JWTHelper.decode).toHaveBeenCalledWith(token, {}, undefined);
      expect(result).toEqual(decodedPayload);
    });

    it('should throw error for invalid token', () => {
      // Arrange
      const token = 'invalid.jwt.token';
      const error = new Error('Invalid token');
      vi.mocked(JWTHelper.decode).mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      expect(() => verifyToken(token)).toThrow('Invalid token');
      expect(JWTHelper.decode).toHaveBeenCalledWith(token, {}, undefined);
    });

    it('should throw error for expired token', () => {
      // Arrange
      const token = 'expired.jwt.token';
      const error = new Error('Token expired');
      error.name = 'TokenExpiredError';
      vi.mocked(JWTHelper.decode).mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      expect(() => verifyToken(token)).toThrow('Token expired');
    });

    it('should throw error for malformed token', () => {
      // Arrange
      const token = 'malformed.token';
      const error = new Error('Malformed token');
      error.name = 'JsonWebTokenError';
      vi.mocked(JWTHelper.decode).mockImplementation(() => {
        throw error;
      });

      // Act & Assert
      expect(() => verifyToken(token)).toThrow('Malformed token');
    });
  });
});
