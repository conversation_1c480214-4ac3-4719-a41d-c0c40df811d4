import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import JWTHelper from './jwt-helper.js';

// Mock jsonwebtoken
const mockJwt = {
  sign: vi.fn(),
  verify: vi.fn(),
  TokenExpiredError: class extends Error {
    name = 'TokenExpiredError';
  },
  JsonWebTokenError: class extends Error {
    name = 'JsonWebTokenError';
  },
};

vi.mock('jsonwebtoken', () => mockJwt);

describe('JWTHelper', () => {
  const TEST_SECRET = 'test-secret-key';
  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    delete process.env.NODE_ENV;
    delete process.env.JWT_EXPIRATION_SECONDS;
    delete process.env.JWT_ALGORITHM;
    delete process.env.JWT_DEV_SECRET;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getSecretKey', () => {
    it('should return provided secret key when valid', () => {
      const result = JWTHelper.getSecretKey(TEST_SECRET);
      expect(result).toBe(TEST_SECRET);
    });

    it('should return dev secret in non-production environment when no secret provided', () => {
      process.env.NODE_ENV = 'development';
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const result = JWTHelper.getSecretKey();

      expect(result).toBe('dev-only-jwt-secret-key');
      expect(consoleSpy).toHaveBeenCalledWith('Using development JWT secret - not suitable for production');

      consoleSpy.mockRestore();
    });

    it('should throw error in production when no secret provided', () => {
      process.env.NODE_ENV = 'production';

      expect(() => JWTHelper.getSecretKey()).toThrow('JWT secret key is required in production');
    });

    it('should use custom dev secret from environment', () => {
      process.env.JWT_DEV_SECRET = 'custom-dev-secret';
      process.env.NODE_ENV = 'development';
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const result = JWTHelper.getSecretKey();

      expect(result).toBe('custom-dev-secret');
      consoleSpy.mockRestore();
    });
  });

  describe('encode', () => {
    it('should encode token with default configuration', () => {
      const mockToken = 'encoded.jwt.token';
      mockJwt.sign.mockReturnValue(mockToken);

      const result = JWTHelper.encode(123, { role: 'user' }, TEST_SECRET);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: '123',
          role: 'user',
          iat: expect.any(Number),
          exp: expect.any(Number),
        }),
        TEST_SECRET,
        { algorithm: 'HS256' }
      );
      expect(result).toBe(mockToken);
    });

    it('should use custom expiration from environment', () => {
      process.env.JWT_EXPIRATION_SECONDS = '7200'; // 2 hours
      const mockToken = 'encoded.jwt.token';
      mockJwt.sign.mockReturnValue(mockToken);

      JWTHelper.encode(123, {}, TEST_SECRET);

      const callArgs = mockJwt.sign.mock.calls[0][0];
      expect(callArgs.exp - callArgs.iat).toBe(7200);
    });

    it('should use custom algorithm from environment', () => {
      process.env.JWT_ALGORITHM = 'HS512';
      const mockToken = 'encoded.jwt.token';
      mockJwt.sign.mockReturnValue(mockToken);

      JWTHelper.encode(123, {}, TEST_SECRET);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.any(Object),
        TEST_SECRET,
        { algorithm: 'HS512' }
      );
    });
  });

  describe('decode', () => {
    it('should decode valid token successfully', () => {
      const mockPayload = {
        sub: '123',
        iat: 1234567890,
        exp: 1234567890 + 3600,
        role: 'user',
      };
      mockJwt.verify.mockReturnValue(mockPayload);

      const result = JWTHelper.decode('valid.jwt.token', {}, TEST_SECRET);

      expect(mockJwt.verify).toHaveBeenCalledWith(
        'valid.jwt.token',
        TEST_SECRET,
        { algorithms: ['HS256'] }
      );
      expect(result).toEqual(mockPayload);
    });

    it('should handle expired token error', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = new mockJwt.TokenExpiredError('Token expired');
      mockJwt.verify.mockImplementation(() => { throw error; });

      expect(() => JWTHelper.decode('expired.token', {}, TEST_SECRET)).toThrow('Token expired');
      expect(consoleSpy).toHaveBeenCalledWith('Expired JWT token', { token: 'expired.token' });

      consoleSpy.mockRestore();
    });

    it('should handle invalid token error', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = new mockJwt.JsonWebTokenError('Invalid token');
      mockJwt.verify.mockImplementation(() => { throw error; });

      expect(() => JWTHelper.decode('invalid.token', {}, TEST_SECRET)).toThrow('Invalid token');
      expect(consoleSpy).toHaveBeenCalledWith('Invalid JWT token', { token: 'invalid.token' });

      consoleSpy.mockRestore();
    });

    it('should handle generic errors', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = new Error('Generic error');
      mockJwt.verify.mockImplementation(() => { throw error; });

      expect(() => JWTHelper.decode('token', {}, TEST_SECRET)).toThrow('Generic error');
      expect(consoleSpy).toHaveBeenCalledWith('Generic JWT error', { error });

      consoleSpy.mockRestore();
    });
  });

  describe('createUserToken', () => {
    it('should create user token with organization', () => {
      const mockToken = 'user.jwt.token';
      mockJwt.sign.mockReturnValue(mockToken);

      const result = JWTHelper.createUserToken(mockUser as any, 'test-org', { role: 'admin' }, TEST_SECRET);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: '1',
          organization: 'test-org',
          role: 'admin',
        }),
        TEST_SECRET,
        { algorithm: 'HS256' }
      );
      expect(result).toBe(mockToken);
    });

    it('should create user token without organization', () => {
      const mockToken = 'user.jwt.token';
      mockJwt.sign.mockReturnValue(mockToken);

      const result = JWTHelper.createUserToken(mockUser as any, null, {}, TEST_SECRET);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: '1',
          organization: 'null',
        }),
        TEST_SECRET,
        { algorithm: 'HS256' }
      );
      expect(result).toBe(mockToken);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token with extended expiration', () => {
      const originalPayload = {
        sub: '123',
        iat: 1234567890,
        exp: 1234567890 + 3600,
        organization: 'test-org',
      };
      const refreshedToken = 'refreshed.jwt.token';

      mockJwt.verify.mockReturnValue(originalPayload);
      mockJwt.sign.mockReturnValue(refreshedToken);

      const result = JWTHelper.refreshToken('original.token', true, TEST_SECRET);

      expect(mockJwt.verify).toHaveBeenCalledWith(
        'original.token',
        TEST_SECRET,
        { algorithms: ['HS256'], ignoreExpiration: true }
      );
      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: '123',
          organization: 'test-org',
          iat: expect.any(Number),
          exp: expect.any(Number),
        }),
        TEST_SECRET,
        { algorithm: 'HS256' }
      );
      expect(result).toBe(refreshedToken);
    });

    it('should refresh token without extending expiration', () => {
      const originalPayload = {
        sub: '123',
        iat: 1234567890,
        exp: 1234567890 + 3600,
        organization: 'test-org',
      };
      const refreshedToken = 'refreshed.jwt.token';

      mockJwt.verify.mockReturnValue(originalPayload);
      mockJwt.sign.mockReturnValue(refreshedToken);

      JWTHelper.refreshToken('original.token', false, TEST_SECRET);

      const signCallArgs = mockJwt.sign.mock.calls[0][0];
      expect(signCallArgs.exp).toBe(originalPayload.exp);
    });

    it('should handle invalid token during refresh', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const error = new mockJwt.JsonWebTokenError('Invalid token');
      mockJwt.verify.mockImplementation(() => { throw error; });

      expect(() => JWTHelper.refreshToken('invalid.token', true, TEST_SECRET)).toThrow('Invalid token');
      expect(consoleSpy).toHaveBeenCalledWith('Cannot refresh invalid token ', { token: 'invalid.token' });

      consoleSpy.mockRestore();
    });
  });
});

  it('should create a supporter token with organization claim', () => {
    const supporterToken = JWTHelper.createSupporterToken(supporter, organization, { extra: 123 }, TEST_SECRET);
    const decoded: any = JWTHelper.decode(supporterToken, {}, TEST_SECRET);
    expect(decoded.sub).toBe(supporter.id);
    expect(decoded.organization).toBe(organization.id);
    expect(decoded.extra).toBe(123);
  });

  it('should refresh a token and update expiration', () => {
    const oldToken = JWTHelper.encode(supporter.id, {}, TEST_SECRET);
    const refreshed = JWTHelper.refreshToken(oldToken, true, TEST_SECRET);
    const decoded: any = JWTHelper.decode(refreshed, {}, TEST_SECRET);
    expect(decoded.sub).toBe(supporter.id);
    expect(decoded.exp).toBeGreaterThan(decoded.iat);
  });

  it('should use fallback secret if none provided', () => {
    process.env.GRASSROOTS_JWT_SECRET_KEY = 'fallback-secret';
    const t = JWTHelper.encode(supporter.id, {}, null);
    const decoded: any = JWTHelper.decode(t, {}, 'fallback-secret');
    expect(decoded.sub).toBe(supporter.id);
  });
});