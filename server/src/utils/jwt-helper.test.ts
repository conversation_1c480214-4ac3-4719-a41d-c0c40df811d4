import  J<PERSON>THelper  from './jwt-helper.js';

const TEST_SECRET = 'test-secret-key';
const supporter = { id: 1 };
const organization = { id: 2 };

describe('JWTHelper', () => {
  let token: string;

  it('should encode and decode a token', () => {
    token = JWTHelper.encode(supporter.id, { foo: 'bar' }, TEST_SECRET);
    const decoded: any = JWTHelper.decode(token, {}, TEST_SECRET);
    expect(decoded.sub).toBe(supporter.id);
    expect(decoded.foo).toBe('bar');
  });

  it('should throw on invalid token', () => {
    expect(() => JWTHelper.decode('invalid.token', {}, TEST_SECRET)).toThrow();
  });

  it('should create a supporter token with organization claim', () => {
    const supporterToken = JWTHelper.createSupporterToken(supporter, organization, { extra: 123 }, TEST_SECRET);
    const decoded: any = JWTHelper.decode(supporterToken, {}, TEST_SECRET);
    expect(decoded.sub).toBe(supporter.id);
    expect(decoded.organization).toBe(organization.id);
    expect(decoded.extra).toBe(123);
  });

  it('should refresh a token and update expiration', () => {
    const oldToken = JWTHelper.encode(supporter.id, {}, TEST_SECRET);
    const refreshed = JWTHelper.refreshToken(oldToken, true, TEST_SECRET);
    const decoded: any = JWTHelper.decode(refreshed, {}, TEST_SECRET);
    expect(decoded.sub).toBe(supporter.id);
    expect(decoded.exp).toBeGreaterThan(decoded.iat);
  });

  it('should use fallback secret if none provided', () => {
    process.env.GRASSROOTS_JWT_SECRET_KEY = 'fallback-secret';
    const t = JWTHelper.encode(supporter.id, {}, null);
    const decoded: any = JWTHelper.decode(t, {}, 'fallback-secret');
    expect(decoded.sub).toBe(supporter.id);
  });
});