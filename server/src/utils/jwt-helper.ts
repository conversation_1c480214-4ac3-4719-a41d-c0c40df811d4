import { User } from "@prisma/client";
import { JwtPayload } from 'jsonwebtoken';

const jwt = require('jsonwebtoken');

interface Organization { id: string | number };
interface SupporterTokenClaims { organization: string | number; [key: string]: any };

export default class JWTHelper {
  private static DEFAULT_EXPIRATION =  60 *  60 *  24 *  30; //  30 days
  private static DEFAULT_ALGORITHM = 'HS256';
  private static DEV_SECRET = 'dev-only-jwt-secret-key';

  private static getConfig() {
    return {
      expirationSeconds:
        Number(process.env.JWT_EXPIRATION_SECONDS) ||
        JWTHelper.DEFAULT_EXPIRATION,
      algorithm:
        process.env.JWT_ALGORITHM ||
        JWTHelper.DEFAULT_ALGORITHM,
      devSecret:
        process.env.JWT_DEV_SECRET ||
        JWTHelper.DEV_SECRET,
    };
  }

  static getSecretKey(secretKey?: string): string {
    const config = this.getConfig();
    
    if (!secretKey || typeof secretKey !== 'string' || !secretKey.trim()) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn('Using development JWT secret - not suitable for production');
        return config.devSecret;
      }
      throw new Error('JWT secret key is required in production');
    }

    return secretKey;
  }

  static encode(
    identifier:string|number,
    extraClaims:{[key:string]:any} = {},
    secretKey?:string,
  ):string{
    const epochNow=Math.floor(Date.now()/1000);
    
    const payload={
      sub:String(identifier),
      iat :epochNow,
      exp :epochNow+this.getConfig().expirationSeconds,
      ...extraClaims
    };

    return jwt.sign(
      payload,
      this.getSecretKey(secretKey),
      {algorithm :this.getConfig().algorithm}
    );
  }

  static decode(
    token:string,
    extraOptions={},
    secretKey?:string
  ):JwtPayload{
    try{
      const options={
        algorithms:[this.getConfig().algorithm],
        ...extraOptions
      };

      return jwt.verify(
        token,
        this.getSecretKey(secretKey),
        options
      ) as JwtPayload;
    }
  
    catch(error){
      if(error instanceof jwt.TokenExpiredError){
        console.error('Expired JWT token',{token});
      }else if(error instanceof jwt.JsonWebTokenError){
        console.error('Invalid JWT token',{token});
      }else{
        console.error(`Generic JWT error`,{error});
      }
      throw error;
    }
  }

  public static createUserToken(
    user :User,
    organizationId ?:string | null ,
    extraData={},
    secret ? :string 
  ):string{
  
    const claims :SupporterTokenClaims={
      organization:String(organizationId),
      ...extraData 
    };

    return this.encode(String(user.id),claims ,secret );
  }

  static refreshToken(
    token:string ,
    extendExpiration=true ,
    secret ? :string 
  ):string{
    try{
      const payload=this.decode(token ,{ignoreExpiration :true},secret );

      const epochNow=Math.floor(Date.now()/1000);
      payload.iat=epochNow;

      if(extendExpiration){
      payload.exp=epochNow+this.getConfig().expirationSeconds ;
      }

      return jwt.sign(
      payload ,
      this.getSecretKey(secret ),
      {algorithm :this .getConfig().algorithm }
      );

    }
    catch(error ){
      if(error instanceof jwt.JsonWebTokenError ){
        console.error ('Cannot refresh invalid token ',{token });
      }
      throw error ;
    }
  }
}
