import bcrypt from 'bcrypt';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './jwt-helper.js';
import { JwtPayload } from 'jsonwebtoken';

const SALT_ROUNDS = 12;

export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, SALT_ROUNDS);
}

export async function verifyPassword(
  password: string,
  hash: string
): Promise<boolean> {
  return await bcrypt.compare(password, hash);
}

export function verifyToken(token: string, secret?: string): JwtPayload {
  try {
    return JWTHelper.decode(token, {}, secret);
  }
  catch (error) {
    throw error;
  }
}
