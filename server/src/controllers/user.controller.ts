import { PrismaClient } from '@prisma/client';
import HTTP_STATUS from "../constants/httpStatus.js";

const prisma = new PrismaClient();

export const createUser = async (req:any, res:any) => {
    console.log("createUser endpoint")
    try {
        const { first_name, password, last_name, email, gender, phone } = req.body;
        const user = await prisma.user.create({
            data: { first_name, password, last_name, email, gender, phone },
        });
        res.status(HTTP_STATUS.CREATED).json({ success: true, data: user });
    } catch (error:any) {
        console.log("Error on createUser", error);
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
            success: false,
            message: "Internal server error",
        });
    }
};

export const getAllUsers = async (req:any, res:any) => {
    try {
        console.log(`${new Date().toISOString()} - All users request hit!`);
        let { page, limit } = req.query;

        if (!page && !limit) {
            page = 1;
            limit = 5;
        }

        if (page <= 0) {
            return res.status(HTTP_STATUS.UNPROCESSABLE_ENTITY).send({
                success: false,
                message: "Page value must be 1 or more",
                payload: null,
            });
        }

        if (limit <= 0) {
            return res.status(HTTP_STATUS.UNPROCESSABLE_ENTITY).send({
                success: false,
                message: "Limit value must be 1 or more",
                payload: null,
            });
        }

        const users = await prisma.user.findMany({
            skip: Number(page - 1) * Number(limit),
            take: Number(limit),
            select: {
                id: true,
                email: true,
                password: true, // Including password for testing
                first_name: true,
                last_name: true,
                gender: true,
                phone: true,
                organization: true,
                emailVerified: true,
                createdAt: true,
            },
        });

        const total = await prisma.user.count();
        return res.status(HTTP_STATUS.OK).send({
            success: true,
            message: "Successfully received all users",
            payload: {
                users: users,
                total: total,
            },
        });
    } catch (error) {
        console.log("Error on getAllUsers", error);
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
            success: false,
            message: "Internal server error",
        });
    }
};

export const getUserById = async (req:any, res:any) => {
    try {
        const { id } = req.params;
        const result = await prisma.user.findFirst({ where: { id: Number(id) } });

        if (result) {
            return res.status(HTTP_STATUS.OK).send({
                success: true,
                message: `Successfully received user with id: ${id}`,
                payload: result,
            });
        }

        return res.status(HTTP_STATUS.NOT_FOUND).send({
            success: false,
            message: "Could not find user",
            payload: null,
        });
    } catch (error) {
        console.log("Error on getUserById", error);
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
            success: false,
            message: "Internal server error",
        });
    }
};

export const updateUser = async (req:any, res:any) => {
    try {
        const { id } = req.params;
        const { first_name, last_name, email, gender, phone } = req.body;
        const user = await prisma.user.update({
            where: { id: Number(id) },
            data: { first_name, last_name, email, gender, phone },
        });
        res.json({ success: true, data: user });
    } catch (error:any) {
        console.log("Error on updateUser", error);
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
            success: false,
            message: "Internal server error",
        });
    }
};

export const deleteUser = async (req:any, res:any) => {
    try {
        await prisma.user.delete({ where: { id: Number(req.params.id) } });
        res.json({ success: true, message: "User deleted" });
    } catch (error:any) {
        console.log("Error on deleteUser", error);
        return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
            success: false,
            message: "Internal server error",
        });
    }
};
