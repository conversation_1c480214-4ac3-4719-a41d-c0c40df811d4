import { Request, Response } from 'express';
import * as authService from '../services/auth.service.js';
import { User } from '@prisma/client';
import J<PERSON><PERSON>elper from '../utils/jwt-helper.js';

export async function register(req: Request, res: Response) {
 try {
    const { email, password, first_name, last_name, gender, phone } = req.body;
    const user = await authService.registerUser({ email, password, first_name, last_name, gender, phone });
    res.status(201).json(user);
 } 
 catch (error:any) {
    res.status(400).json({ error: error.message });
 }
}

export async function login(req: Request, res: Response) {
 try {
    const { email, password } = req.body;
    const token = await authService.loginUser(email, password);
    
    res.cookie('token', token, {
    httpOnly: true,
    secure: true,
    sameSite: 'strict', 
    maxAge: Number(process.env.JWT_EXPIRATION_SECONDS)
    });    
    res.status(200).json({ token });
 } catch (error:any) {
    res.status(401).json({ error: error.message });
 }
}
