import { Request, Response } from 'express';
import * as authService from '../services/auth.service.js';
import { User } from '@prisma/client';
import J<PERSON>THelper from '../utils/jwt-helper.js';

export async function register(req: Request, res: Response) {
 try {
    const { email, password, first_name, last_name, gender, phone } = req.body;
    const user = await authService.registerUser({ email, password, first_name, last_name, gender, phone });
    res.status(201).json(user);
 } 
 catch (error:any) {
    res.status(400).json({ error: error.message });
 }
}

export async function login(req: Request, res: Response) {
 try {
    const { email, password } = req.body;
    const token = await authService.loginUser(email, password);

    // Cookie configuration that works in both development and production
    const isProduction = process.env.NODE_ENV === 'production';
    const maxAge = Number(process.env.JWT_EXPIRATION_SECONDS) * 1000 || 30 * 24 * 60 * 60 * 1000; // Convert to milliseconds

    res.cookie('token', token, {
      httpOnly: true,
      secure: isProduction, // Only secure in production (HTTPS)
      sameSite: 'strict',
      maxAge: maxAge
    });
    res.status(200).json({ token });
 } catch (error:any) {
    res.status(401).json({ error: error.message });
 }
}
