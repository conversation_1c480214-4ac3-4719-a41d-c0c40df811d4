import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Request, Response } from 'express';
import { register, login } from './auth.controller.js';
import * as authService from '../services/auth.service.js';

vi.mock('../services/auth.service.js', () => ({
  registerUser: vi.fn(),
  loginUser: vi.fn(),
}));

describe('Auth Controller', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    vi.clearAllMocks();

    mockRequest = { body: {} };
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      cookie: vi.fn().mockReturnThis(),
    };
  });

  describe('register', () => {
    it('should return 201 on successful registration', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        gender: 'MALE',
        phone: '+1234567890',
      };
      const createdUser = { id: 1, email: userData.email, first_name: 'John', last_name: 'Doe', createdAt: new Date() };

      mockRequest.body = userData;
      vi.mocked(authService.registerUser).mockResolvedValue(createdUser);

      await register(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(createdUser);
    });

    it('should return 400 on registration error', async () => {
      mockRequest.body = { email: '<EMAIL>' };
      vi.mocked(authService.registerUser).mockRejectedValue(new Error('Email already in use'));

      await register(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Email already in use' });
    });
  });

  describe('login', () => {
    it('should return 200 and set cookie on successful login', async () => {
      const loginData = { email: '<EMAIL>', password: 'password123' };
      const mockToken = 'jwt.token.here';

      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockResolvedValue(mockToken);

      await login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ token: mockToken });
      expect(mockResponse.cookie).toHaveBeenCalledWith('token', mockToken, expect.objectContaining({
        httpOnly: true,
        sameSite: 'strict',
      }));
    });

    it('should return 401 on login error', async () => {
      mockRequest.body = { email: '<EMAIL>', password: 'wrong' };
      vi.mocked(authService.loginUser).mockRejectedValue(new Error('Invalid credentials'));

      await login(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Invalid credentials' });
      expect(mockResponse.cookie).not.toHaveBeenCalled();
    });
  });
});
