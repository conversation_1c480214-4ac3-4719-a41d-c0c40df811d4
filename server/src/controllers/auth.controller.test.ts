import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { Request, Response } from 'express';
import { register, login } from './auth.controller.js';
import * as authService from '../services/auth.service.js';

// Mock auth service
vi.mock('../services/auth.service.js', () => ({
  registerUser: vi.fn(),
  loginUser: vi.fn(),
}));

describe('Auth Controller', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockRequest = {
      body: {},
    };
    
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      cookie: vi.fn().mockReturnThis(),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
    // Clean up environment variables
    delete process.env.NODE_ENV;
    delete process.env.JWT_EXPIRATION_SECONDS;
  });

  describe('register', () => {
    const mockUserData = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'John',
      last_name: 'Doe',
      gender: 'MALE',
      phone: '+1234567890',
    };

    it('should successfully register a new user', async () => {
      // Arrange
      const createdUser = {
        id: 1,
        email: mockUserData.email,
        first_name: mockUserData.first_name,
        last_name: mockUserData.last_name,
        createdAt: new Date(),
      };
      
      mockRequest.body = mockUserData;
      vi.mocked(authService.registerUser).mockResolvedValue(createdUser);

      // Act
      await register(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.registerUser).toHaveBeenCalledWith(mockUserData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(createdUser);
    });

    it('should handle registration errors', async () => {
      // Arrange
      const error = new Error('Email already in use');
      mockRequest.body = mockUserData;
      vi.mocked(authService.registerUser).mockRejectedValue(error);

      // Act
      await register(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.registerUser).toHaveBeenCalledWith(mockUserData);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Email already in use' });
    });

    it('should handle missing required fields', async () => {
      // Arrange
      const incompleteData = { email: '<EMAIL>' };
      const error = new Error('Missing required fields');
      mockRequest.body = incompleteData;
      vi.mocked(authService.registerUser).mockRejectedValue(error);

      // Act
      await register(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.registerUser).toHaveBeenCalledWith(incompleteData);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Missing required fields' });
    });
  });

  describe('login', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should successfully login user in development environment', async () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      process.env.JWT_EXPIRATION_SECONDS = '3600';
      const mockToken = 'jwt.token.here';
      
      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockResolvedValue(mockToken);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.loginUser).toHaveBeenCalledWith(loginData.email, loginData.password);
      expect(mockResponse.cookie).toHaveBeenCalledWith('token', mockToken, {
        httpOnly: true,
        secure: false, // Should be false in development
        sameSite: 'strict',
        maxAge: 3600000, // 3600 seconds * 1000 milliseconds
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ token: mockToken });
    });

    it('should successfully login user in production environment', async () => {
      // Arrange
      process.env.NODE_ENV = 'production';
      process.env.JWT_EXPIRATION_SECONDS = '7200';
      const mockToken = 'jwt.token.here';
      
      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockResolvedValue(mockToken);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.loginUser).toHaveBeenCalledWith(loginData.email, loginData.password);
      expect(mockResponse.cookie).toHaveBeenCalledWith('token', mockToken, {
        httpOnly: true,
        secure: true, // Should be true in production
        sameSite: 'strict',
        maxAge: 7200000, // 7200 seconds * 1000 milliseconds
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({ token: mockToken });
    });

    it('should use default maxAge when JWT_EXPIRATION_SECONDS is not set', async () => {
      // Arrange
      delete process.env.JWT_EXPIRATION_SECONDS;
      const mockToken = 'jwt.token.here';
      
      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockResolvedValue(mockToken);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(mockResponse.cookie).toHaveBeenCalledWith('token', mockToken, {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days default
      });
    });

    it('should handle invalid credentials', async () => {
      // Arrange
      const error = new Error('Invalid credentials');
      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockRejectedValue(error);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.loginUser).toHaveBeenCalledWith(loginData.email, loginData.password);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Invalid credentials' });
      expect(mockResponse.cookie).not.toHaveBeenCalled();
    });

    it('should handle missing email or password', async () => {
      // Arrange
      const incompleteData = { email: '<EMAIL>' }; // Missing password
      const error = new Error('Password is required');
      mockRequest.body = incompleteData;
      vi.mocked(authService.loginUser).mockRejectedValue(error);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.loginUser).toHaveBeenCalledWith('<EMAIL>', undefined);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Password is required' });
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      mockRequest.body = loginData;
      vi.mocked(authService.loginUser).mockRejectedValue(error);

      // Act
      await login(mockRequest as Request, mockResponse as Response);

      // Assert
      expect(authService.loginUser).toHaveBeenCalledWith(loginData.email, loginData.password);
      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Database connection failed' });
    });
  });
});
