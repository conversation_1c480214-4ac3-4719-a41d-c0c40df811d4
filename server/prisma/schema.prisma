// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         Int    @id @default(autoincrement())
  first_name String @db.VarChar(255)
  last_name  String @db.VarChar(255)
  email      String @db.VarChar(255)
  gender     Gender
  phone      String @db.VarChar(100)

  @@map("user")
}

enum Gender {
  MALE
  FEMALE
  OTHER
}
