import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

async function main() {
    await prisma.user.createMany({
        data: [
            {
                id: 1,
                first_name: "<PERSON><PERSON>",
                last_name: "<PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMAL<PERSON>",
                phone: "************",
            },
            {
                id: 2,
                first_name: "<PERSON><PERSON>",
                last_name: "<PERSON><PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 3,
                first_name: "<PERSON><PERSON><PERSON>",
                last_name: "<PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMAL<PERSON>",
                phone: "************",
            },
            {
                id: 4,
                first_name: "<PERSON>",
                last_name: "<PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "<PERSON><PERSON>",
                phone: "************",
            },
            {
                id: 5,
                first_name: "<PERSON><PERSON>",
                last_name: "Furnival",
                email: "<EMAIL>",
                gender: "FEMAL<PERSON>",
                phone: "************",
            },
            {
                id: 6,
                first_name: "<PERSON>",
                last_name: "Cosford",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 7,
                first_name: "Caitrin",
                last_name: "Currer",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 8,
                first_name: "Jamal",
                last_name: "Dosedale",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 9,
                first_name: "Antony",
                last_name: "Crawshay",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 10,
                first_name: "Reine",
                last_name: "Zamorrano",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 11,
                first_name: "Flor",
                last_name: "Swainsbury",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 12,
                first_name: "Hyacinthe",
                last_name: "Bowley",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 13,
                first_name: "Nanete",
                last_name: "Peteri",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 14,
                first_name: "Xena",
                last_name: "McGlade",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 15,
                first_name: "Merill",
                last_name: "Sainsbury-Brown",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 16,
                first_name: "Hortensia",
                last_name: "Kneller",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 17,
                first_name: "Florance",
                last_name: "Bingall",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 18,
                first_name: "Merrill",
                last_name: "Priestley",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 19,
                first_name: "Laura",
                last_name: "Acheson",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 20,
                first_name: "Orv",
                last_name: "Tatlowe",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 21,
                first_name: "Oren",
                last_name: "Pretorius",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 22,
                first_name: "Byran",
                last_name: "Kunneke",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 23,
                first_name: "Norman",
                last_name: "Phillcox",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 24,
                first_name: "Mellisent",
                last_name: "Coopland",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 25,
                first_name: "Bev",
                last_name: "Rosenstiel",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 26,
                first_name: "Pincas",
                last_name: "Donaher",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 27,
                first_name: "Magdalen",
                last_name: "MacAindreis",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 28,
                first_name: "Nana",
                last_name: "Glencrosche",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 29,
                first_name: "Willie",
                last_name: "Landreth",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 30,
                first_name: "Roland",
                last_name: "McKue",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 31,
                first_name: "Geordie",
                last_name: "Bailey",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 32,
                first_name: "Archibaldo",
                last_name: "Wellen",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 33,
                first_name: "Thaddeus",
                last_name: "Lidgey",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 34,
                first_name: "Bartie",
                last_name: "De Metz",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 35,
                first_name: "Hirsch",
                last_name: "Anand",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 36,
                first_name: "Vere",
                last_name: "Eakens",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 37,
                first_name: "Anetta",
                last_name: "Conklin",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 38,
                first_name: "Rufe",
                last_name: "Glassard",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 39,
                first_name: "Tiffany",
                last_name: "Catenot",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 40,
                first_name: "Anne",
                last_name: "Willison",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 41,
                first_name: "Hailee",
                last_name: "De Blasio",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 42,
                first_name: "Hazlett",
                last_name: "Gibbins",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 43,
                first_name: "Daniel",
                last_name: "Dufall",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 44,
                first_name: "Thomasine",
                last_name: "Hamsher",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 45,
                first_name: "Jaclyn",
                last_name: "Missen",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 46,
                first_name: "Franzen",
                last_name: "Scarfe",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 47,
                first_name: "Garey",
                last_name: "Morrid",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 48,
                first_name: "Eal",
                last_name: "Rospars",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 49,
                first_name: "Norbert",
                last_name: "Chamney",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 50,
                first_name: "Ronica",
                last_name: "Fitchen",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
        ],
    });
}

async function init() {
    const users = await prisma.user.count();

    if (users === 0) {
        main()
            .then(async () => {
                await prisma.$disconnect();
            })
            .catch(async (e) => {
                console.error(e);
                await prisma.$disconnect();
                process.exit(1);
            });
        console.log("Database is successully seeded");
    } else {
        console.log("Database seeding is not needed");
    }
}

init();
