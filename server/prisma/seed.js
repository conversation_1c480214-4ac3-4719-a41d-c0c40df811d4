import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

async function main() {
    await prisma.user.createMany({
        data: [
            {
                id: 1,
                password: "hashpassword",
                first_name: "<PERSON><PERSON>",
                last_name: "<PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMAL<PERSON>",
                phone: "************",
            },
            {
                id: 2,
                password: "hashpassword",
                first_name: "<PERSON><PERSON>",
                last_name: "<PERSON><PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 3,
                password: "hashpassword",
                first_name: "<PERSON><PERSON><PERSON>",
                last_name: "<PERSON><PERSON><PERSON>",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 4,
                password: "hashpassword",
                first_name: "<PERSON>",
                last_name: "<PERSON><PERSON><PERSON>",
                email: "g<PERSON><PERSON><EMAIL>",
                gender: "<PERSON><PERSON>",
                phone: "************",
            },
            {
                id: 5,
                password: "hashpassword",
                first_name: "<PERSON><PERSON>",
                last_name: "Furn<PERSON>",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 6,
                password: "hashpassword",
                first_name: "Guy",
                last_name: "Cosford",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 7,
                password: "hashpassword",
                first_name: "Caitrin",
                last_name: "Currer",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 8,
                password: "hashpassword",
                first_name: "Jamal",
                last_name: "Dosedale",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 9,
                password: "hashpassword",
                first_name: "Antony",
                last_name: "Crawshay",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 10,
                password: "hashpassword",
                first_name: "Reine",
                last_name: "Zamorrano",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 11,
                password: "hashpassword",
                first_name: "Flor",
                last_name: "Swainsbury",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 12,
                password: "hashpassword",
                first_name: "Hyacinthe",
                last_name: "Bowley",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 13,
                password: "hashpassword",
                first_name: "Nanete",
                last_name: "Peteri",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 14,
                password: "hashpassword",
                first_name: "Xena",
                last_name: "McGlade",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 15,
                password: "hashpassword",
                first_name: "Merill",
                last_name: "Sainsbury-Brown",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 16,
                password: "hashpassword",
                first_name: "Hortensia",
                last_name: "Kneller",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 17,
                password: "hashpassword",
                first_name: "Florance",
                last_name: "Bingall",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 18,
                password: "hashpassword",
                first_name: "Merrill",
                last_name: "Priestley",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 19,
                password: "hashpassword",
                first_name: "Laura",
                last_name: "Acheson",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 20,
                password: "hashpassword",
                first_name: "Orv",
                last_name: "Tatlowe",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 21,
                password: "hashpassword",
                first_name: "Oren",
                last_name: "Pretorius",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 22,
                password: "hashpassword",
                first_name: "Byran",
                last_name: "Kunneke",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 23,
                password: "hashpassword",
                first_name: "Norman",
                last_name: "Phillcox",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 24,
                password: "hashpassword",
                first_name: "Mellisent",
                last_name: "Coopland",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 25,
                password: "hashpassword",
                first_name: "Bev",
                last_name: "Rosenstiel",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 26,
                password: "hashpassword",
                first_name: "Pincas",
                last_name: "Donaher",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 27,
                password: "hashpassword",
                first_name: "Magdalen",
                last_name: "MacAindreis",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 28,
                password: "hashpassword",
                first_name: "Nana",
                last_name: "Glencrosche",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 29,
                password: "hashpassword",
                first_name: "Willie",
                last_name: "Landreth",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 30,
                password: "hashpassword",
                first_name: "Roland",
                last_name: "McKue",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 31,
                password: "hashpassword",
                first_name: "Geordie",
                last_name: "Bailey",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 32,
                password: "hashpassword",
                first_name: "Archibaldo",
                last_name: "Wellen",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 33,
                password: "hashpassword",
                first_name: "Thaddeus",
                last_name: "Lidgey",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 34,
                password: "hashpassword",
                first_name: "Bartie",
                last_name: "De Metz",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 35,
                password: "hashpassword",
                first_name: "Hirsch",
                last_name: "Anand",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 36,
                password: "hashpassword",
                first_name: "Vere",
                last_name: "Eakens",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 37,
                password: "hashpassword",
                first_name: "Anetta",
                last_name: "Conklin",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 38,
                password: "hashpassword",
                first_name: "Rufe",
                last_name: "Glassard",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 39,
                password: "hashpassword",
                first_name: "Tiffany",
                last_name: "Catenot",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 40,
                password: "hashpassword",
                first_name: "Anne",
                last_name: "Willison",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 41,
                password: "hashpassword",
                first_name: "Hailee",
                last_name: "De Blasio",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 42,
                password: "hashpassword",
                first_name: "Hazlett",
                last_name: "Gibbins",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 43,
                password: "hashpassword",
                first_name: "Daniel",
                last_name: "Dufall",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 44,
                password: "hashpassword",
                first_name: "Thomasine",
                last_name: "Hamsher",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 45,
                password: "hashpassword",
                first_name: "Jaclyn",
                last_name: "Missen",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
            {
                id: 46,
                password: "hashpassword",
                first_name: "Franzen",
                last_name: "Scarfe",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 47,
                password: "hashpassword",
                first_name: "Garey",
                last_name: "Morrid",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 48,
                password: "hashpassword",
                first_name: "Eal",
                last_name: "Rospars",
                email: "<EMAIL>",
                gender: "MALE",
                phone: "************",
            },
            {
                id: 49,
                password: "hashpassword",
                first_name: "Norbert",
                last_name: "Chamney",
                email: "<EMAIL>",
                gender: "OTHER",
                phone: "************",
            },
            {
                id: 50,
                password: "hashpassword",
                first_name: "Ronica",
                last_name: "Fitchen",
                email: "<EMAIL>",
                gender: "FEMALE",
                phone: "************",
            },
        ],
    });
}

async function init() {
    const users = await prisma.user.count();

    if (users === 0) {
        main()
            .then(async () => {
                await prisma.$disconnect();
            })
            .catch(async (e) => {
                console.error(e);
                await prisma.$disconnect();
                process.exit(1);
            });
        console.log("Database is successully seeded");
    } else {
        console.log("Database seeding is not needed");
    }
}

init();
