# Test Users API

## Test if users endpoint returns password field

```bash
# Test the getAllUsers endpoint
curl -X GET "http://localhost:4000/api/users/all?page=1&limit=3"
```

## Test a specific user
```bash
# Test getUserById endpoint  
curl -X GET "http://localhost:4000/api/user/1"
```

## Check if seed was applied
```bash
# In the server directory, run:
cd server && node prisma/seed.js
```

## Apply seed manually if needed
```bash
# If the seed wasn't applied, run:
cd server && npx prisma db seed
```

## Or run the seed file directly
```bash
cd server && node prisma/seed.js
```
