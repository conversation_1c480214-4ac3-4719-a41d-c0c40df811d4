{"name": "crud-server-express", "version": "1.0.0", "description": "", "main": "src/app.js", "type": "module", "dependencies": {"@prisma/client": "^5.22.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "nodemon": "^2.0.20"}, "prisma": {"seed": "node ./prisma/seed.js"}, "scripts": {"dev": "nodemon --watch src --exec ts-node src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.0", "prisma": "^5.22.0", "typescript": "^5.8.3"}}