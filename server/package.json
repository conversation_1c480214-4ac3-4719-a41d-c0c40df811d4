{"name": "crud-server-express", "version": "1.0.0", "description": "", "main": "src/app.js", "type": "module", "dependencies": {"@prisma/client": "^6.11.1", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "nodemon": "^2.0.20"}, "prisma": {"seed": "node ./prisma/seed.js"}, "scripts": {"dev": "nodemon --watch src --exec ts-node src/app.ts", "build": "tsc", "start": "node dist/src/app.js", "test": "yarn vitest run"}, "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.0", "prisma": "^5.22.0", "typescript": "^5.8.3", "vitest": "^3.2.4"}}