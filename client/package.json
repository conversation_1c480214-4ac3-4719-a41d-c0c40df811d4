{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "test": "yarn vitest run", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host"}, "dependencies": {"@mantine/core": "^8.1.0", "@mantine/hooks": "^8.1.0", "@mantine/notifications": "^8.1.0", "axios": "^1.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1", "zod": "^3.25.61"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/jest": "^30.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "vite": "^5.2.0", "vitest": "^3.2.4"}}