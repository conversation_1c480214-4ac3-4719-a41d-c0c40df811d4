.home {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header {
  font-size: 1.5rem;
  font-weight: bold;
}

.table {
  width: 100%;
}

.table th:nth-child(1) {
  width: 10%;
}

.table th:nth-child(2),
th:nth-child(3),
th:nth-child(5) {
  width: 15%;
}

.table th:nth-child(4) {
  width: 25%;
}

.table th:nth-child(6) {
  width: 20%;
}

.table td,
th {
  /* border={"1px"} style={{ width: "100%" }} */
  border: 1px solid gray;
  padding: 0.25rem 0.75rem;
}

.table .thead {
  /* style={{ backgroundColor: "cornflowerblue", color: "azure" }} */
  background-color: cornflowerblue;
  color: azure;
}

.table .tbody {
  background-color: rgb(216, 244, 255);
}

.table .tbody tr {
  cursor: pointer;
  transition: 0.15s background-color;
}

.table .tbody tr:hover {
  background-color: skyblue;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.pagination button {
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  transition: 0.15s background-color;
  background-color: rgb(216, 244, 255);
}

.pagination button:hover,
.button-active {
  background-color: cornflowerblue;
}
