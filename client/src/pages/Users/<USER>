import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { showNotification } from '@mantine/notifications';

import { deleteUser, createUser } from "../../api/users";
import useUsers from "../../hooks/useUsers";
import "./Users.css";

const Users = () => {
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 5,
  });
  const navigate = useNavigate();
  const { users, totalUsers, loading, error, refetchUsers } = useUsers(pagination.page, pagination.limit);

  const [form, setForm] = useState({
    first_name: "Test",
    last_name: "User",
    email: "<EMAIL>",
    gender: "MALE",
    phone: "123",
  });
  const [creating, setCreating] = useState(false);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setCreating(true);
    try {
      await createUser(form);
      showNotification({
        title: 'Sucesso',
        message: 'Usuário criado com sucesso!',
        color: 'green',
      });
    } catch (err) {
      showNotification({
        title: 'Erro',
        message: 'Falha ao criar usuário',
        color: 'red',
      });
    } finally {
      setCreating(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteUser(id);
      showNotification({
        title: 'Usuário deletado',
        message: `Usuário ${id} removido com sucesso!`,
        color: 'green',
      });
      refetchUsers();
    } catch (err) {
      showNotification({
        title: 'Erro',
        message: 'Falha ao deletar usuário',
        color: 'red',
      });
    }
  };

  return (
    <div className="home">
      <button onClick={() => showNotification({ title: 'Test', message: 'Hello', color: 'blue' })}>Test Notification</button>
      <span className="header">Lista de Usuarios  ({totalUsers})</span>
      
      {loading && <span className="loading">Carregando...</span>}
      
      <table className="table">

        <thead className="thead">
          <tr>
            <th>id</th>
            <th>Actions</th>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Email</th>
            <th>gender</th>
            <th>phone</th>
          </tr>
        </thead>

        <tbody className="tbody">
          {users.map(({ id, first_name, last_name, email, gender, phone }) => {
            return (
              <tr
                key={id}
              >
                <td>
                  <button onClick={() => {
                    navigate(`/user/${id}`);
                  }}>
                    {id}
                  </button>
                  </td>
                <td>
                  <button onClick={() => handleDelete(id)}>Delete</button>
                </td>
                <td>{first_name}</td>
                <td>{last_name}</td>
                <td>{email}</td>
                <td>{gender}</td>
                <td>{phone}</td>

              </tr>
            );
          })}
        </tbody>

      </table>

      <div className="pagination">
        {Array(Math.max(1, Math.ceil(totalUsers / pagination.limit)))
          .fill(0)
          .map((element, i) => {
            return (
              <button
                key={i}
                className={i + 1 === pagination.page ? "button-active" : ""}
                onClick={() =>
                  setPagination((prevState) => ({ ...prevState, page: i + 1 }))
                }
              >
                {i + 1}
              </button>
            );
          })}
      </div>

      <form onSubmit={handleSubmit} style={{ marginBottom: 16 }}>
        <input name="first_name" value={form.first_name} onChange={handleChange} placeholder="First Name" required />
        <input name="last_name" value={form.last_name} onChange={handleChange} placeholder="Last Name" required />
        <input name="email" value={form.email} onChange={handleChange} placeholder="Email" required />
        <select name="gender" value={form.gender} onChange={handleChange} required>
          <option value="MALE">MALE</option>
          <option value="FEMALE">FEMALE</option>
        </select>
        <input name="phone" value={form.phone} onChange={handleChange} placeholder="Phone" required />
        <button type="submit" disabled={creating}>
          {creating ? "Creating..." : "Create User"}
        </button>
      </form>

    </div>
  );
};

export default Users;
