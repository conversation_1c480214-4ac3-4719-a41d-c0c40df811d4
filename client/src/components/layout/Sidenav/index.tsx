import React from 'react';
import {NavLink} from "react-router-dom";

import './styles.css'; 

const Sidenav = () => {
    return (
        <div className='sidenav'>
            <div className='sidenav-header'>
                Logo
            </div>
            <div className='sidenav-links'>
                <NavLink to="/" className={({isActive}) => isActive ? 'active' : ''}>
                    <span className='icon'>Home</span>
                </NavLink>
                <NavLink to="/dashboard" className={({isActive}) => isActive ? 'active' : ''}>
                    <span className='icon'>Dashboard</span>
                </NavLink>
                <NavLink to="/user" className={({isActive}) => isActive ? 'active' : ''}>
                    <span className='icon'>Clientes</span>
                </NavLink>
            </div>
        </div>
    )
}

export default Sidenav;
