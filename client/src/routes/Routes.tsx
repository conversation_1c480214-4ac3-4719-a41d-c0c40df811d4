import React from "react";
import { Navigate, Route, Routes } from "react-router-dom";

import Layout from "../components/layout";

import Login from "../pages/Login";

import Home from "../pages/Home";
import UserDetail from "../pages/Users/<USER>";
import Dashboard from "../pages/Dashboard";

import Users from "../pages/Users";

const AppRoutes = () => (
  <Routes>
    <Route path="/login" element={<Login />} />

    <Route path="/" element={<Layout />} >
      <Route index element={<Home />} />
      <Route path="/home" element={<Navigate to="/" />} />
      <Route path="/dashboard" element={<Dashboard />} />

      <Route path="/user" element={<Users />} />
      <Route path="/user/:id" element={<UserDetail />} />
    </Route>

  </Routes>
);

export default AppRoutes;