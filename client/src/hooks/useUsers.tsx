import { useEffect, useState, useCallback } from "react";
import { z } from "zod";
import { getUsers } from "../api/users";

const UserSchema = z.object({
  id: z.number(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string(),
  gender: z.string(),
  phone: z.string(),
});

const UsersResponseSchema = z.object({
  users: z.array(UserSchema),
  total: z.number(),
});

const useUsers = (page: number, limit: number) => {
  const [users, setUsers] = useState<z.infer<typeof UserSchema>[]>([]);
  const [totalUsers, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);

  const fetchUsers = useCallback(() => {
    setLoading(true);
    setError(null);
    getUsers(page, limit)
      .then(({ data }) => {
        const parseResult = UsersResponseSchema.safeParse(data.payload);
        if (parseResult.success) {
          setUsers(parseResult.data.users);
          setTotal(parseResult.data.total);
        } else {
          setError("Invalid data format");
          setUsers([]);
        }
      })
      .catch((err) => {
        setError(err.message || "Error fetching users");
        setUsers([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [page, limit]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return { users, totalUsers, loading, error, refetchUsers: fetchUsers };
};

export default useUsers;