version: '3'

tasks:
  test:
    desc: Run all tests in test profile
    cmds:
      - docker compose --profile test up

  test:backend:
    desc: Run backend tests
    cmds:
      - docker compose --profile test up test-backend

  up:
    desc: Start Docker containers
    cmds:
      - docker compose up

  up:build:
    desc: Build Docker containers
    cmds:
      - docker compose up --build

  build:
    desc: Build Docker images
    cmds:
      - docker compose build --no-cache
  
  down:
    desc: Drop containers
    cmds:
      - docker compose down

  rebuild:
    desc: Build Docker images without cache and start containers
    cmds:
      - docker compose down -v
      - docker compose build --no-cache
      - docker compose up --build

  prisma:create:
    desc: Run prisma migrate and generate
    cmds:
      - docker compose exec server npx prisma migrate dev --name {{.RESOURCE_NAME}} --schema=./prisma/schema.prisma
      - docker compose exec server npx prisma generate --schema=./prisma/schema.prisma
    vars:
      RESOURCE_NAME:
        sh: echo "{{.CLI_ARGS}}"

  seed:
    desc: Seeds the database with the contents of prisma/seed.js
    cmds:
      - npx prisma db seed